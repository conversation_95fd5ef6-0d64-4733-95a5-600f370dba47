"""
安全模块 - 密钥管理和加密功能
提供多种密钥存储和管理方案
"""
import base64
import hashlib
import os
from typing import Optional, Dict, Any
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import structlog

from app.core.config import settings
from app.core.exceptions import ConfigurationError, AuthenticationError

logger = structlog.get_logger(__name__)


class EncryptionManager:
    """加密管理器"""
    
    def __init__(self, encryption_key: str):
        """初始化加密管理器"""
        try:
            # 如果提供的是密码，则派生密钥
            if len(encryption_key) != 44:  # Fernet密钥的base64编码长度
                self._fernet = Fernet(self._derive_key(encryption_key))
            else:
                self._fernet = Fernet(encryption_key.encode())
        except Exception as e:
            logger.error("加密管理器初始化失败", error=str(e))
            raise ConfigurationError(f"加密密钥配置错误: {e}")
    
    def _derive_key(self, password: str) -> bytes:
        """从密码派生加密密钥"""
        password_bytes = password.encode()
        salt = b'customer_service_salt'  # 生产环境应使用随机盐
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key
    
    def encrypt(self, data: str) -> str:
        """加密数据"""
        try:
            encrypted_data = self._fernet.encrypt(data.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error("数据加密失败", error=str(e))
            raise ConfigurationError(f"数据加密失败: {e}")
    
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据"""
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = self._fernet.decrypt(decoded_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error("数据解密失败", error=str(e))
            raise ConfigurationError(f"数据解密失败: {e}")


class KeyManager:
    """密钥管理器 - 支持多种存储方式"""
    
    def __init__(self):
        self.encryption_manager = EncryptionManager(settings.ENCRYPTION_KEY)
        self._cache: Dict[str, str] = {}
    
    async def get_api_key(self, provider: str, key_type: str = "default") -> Optional[str]:
        """
        获取API密钥
        
        Args:
            provider: 提供商名称 (openai, azure, anthropic等)
            key_type: 密钥类型 (default, backup等)
        
        Returns:
            解密后的API密钥
        """
        cache_key = f"{provider}:{key_type}"
        
        # 先检查缓存
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        try:
            # 方案1: 从数据库获取加密的密钥
            encrypted_key = await self._get_key_from_db(provider, key_type)
            if encrypted_key:
                decrypted_key = self.encryption_manager.decrypt(encrypted_key)
                self._cache[cache_key] = decrypted_key
                return decrypted_key
            
            # 方案2: 从环境变量获取（开发环境）
            env_key = f"{provider.upper()}_{key_type.upper()}_API_KEY"
            env_value = os.getenv(env_key)
            if env_value:
                self._cache[cache_key] = env_value
                return env_value
            
            # 方案3: 从外部密钥管理服务获取（生产环境推荐）
            # external_key = await self._get_key_from_external_service(provider, key_type)
            # if external_key:
            #     self._cache[cache_key] = external_key
            #     return external_key
            
            logger.warning("未找到API密钥", provider=provider, key_type=key_type)
            return None
            
        except Exception as e:
            logger.error("获取API密钥失败", provider=provider, key_type=key_type, error=str(e))
            raise ConfigurationError(f"获取API密钥失败: {e}")
    
    async def _get_key_from_db(self, provider: str, key_type: str) -> Optional[str]:
        """从数据库获取加密的密钥"""
        # 这里需要实现数据库查询逻辑
        # 示例实现：
        from app.db.database import get_db_session
        
        async with get_db_session() as session:
            # 查询密钥表
            # result = await session.execute(
            #     select(ApiKey.encrypted_value)
            #     .where(ApiKey.provider == provider)
            #     .where(ApiKey.key_type == key_type)
            #     .where(ApiKey.is_active == True)
            # )
            # row = result.first()
            # return row[0] if row else None
            
            # 临时返回None，实际实现时需要连接数据库
            return None
    
    async def store_api_key(self, provider: str, key_value: str, key_type: str = "default") -> bool:
        """
        存储API密钥到数据库
        
        Args:
            provider: 提供商名称
            key_value: 原始密钥值
            key_type: 密钥类型
        
        Returns:
            是否存储成功
        """
        try:
            # 加密密钥
            encrypted_value = self.encryption_manager.encrypt(key_value)
            
            # 存储到数据库
            success = await self._store_key_to_db(provider, encrypted_value, key_type)
            
            if success:
                # 更新缓存
                cache_key = f"{provider}:{key_type}"
                self._cache[cache_key] = key_value
                logger.info("API密钥存储成功", provider=provider, key_type=key_type)
            
            return success
            
        except Exception as e:
            logger.error("存储API密钥失败", provider=provider, key_type=key_type, error=str(e))
            return False
    
    async def _store_key_to_db(self, provider: str, encrypted_value: str, key_type: str) -> bool:
        """将加密的密钥存储到数据库"""
        # 这里需要实现数据库存储逻辑
        # 示例实现：
        from app.db.database import get_db_session
        
        try:
            async with get_db_session() as session:
                # 插入或更新密钥记录
                # api_key = ApiKey(
                #     provider=provider,
                #     key_type=key_type,
                #     encrypted_value=encrypted_value,
                #     is_active=True
                # )
                # session.add(api_key)
                # await session.commit()
                return True
        except Exception as e:
            logger.error("数据库存储密钥失败", error=str(e))
            return False
    
    def clear_cache(self):
        """清空密钥缓存"""
        self._cache.clear()
        logger.info("密钥缓存已清空")


# 全局密钥管理器实例
key_manager = KeyManager()


# 其他密钥管理方案的示例实现

class HashiCorpVaultKeyManager:
    """HashiCorp Vault密钥管理器（生产环境推荐）"""
    
    def __init__(self, vault_url: str, vault_token: str):
        self.vault_url = vault_url
        self.vault_token = vault_token
        # 这里可以初始化Vault客户端
    
    async def get_secret(self, path: str) -> Dict[str, Any]:
        """从Vault获取密钥"""
        # 实现Vault API调用
        pass


class AWSSecretsManagerKeyManager:
    """AWS Secrets Manager密钥管理器"""
    
    def __init__(self, region_name: str):
        self.region_name = region_name
        # 这里可以初始化AWS客户端
    
    async def get_secret(self, secret_name: str) -> str:
        """从AWS Secrets Manager获取密钥"""
        # 实现AWS Secrets Manager API调用
        pass


class AzureKeyVaultKeyManager:
    """Azure Key Vault密钥管理器"""
    
    def __init__(self, vault_url: str):
        self.vault_url = vault_url
        # 这里可以初始化Azure客户端
    
    async def get_secret(self, secret_name: str) -> str:
        """从Azure Key Vault获取密钥"""
        # 实现Azure Key Vault API调用
        pass
