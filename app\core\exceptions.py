"""
自定义异常类
"""
from typing import Any, Dict, Optional


class CustomException(Exception):
    """自定义基础异常类"""
    
    def __init__(
        self,
        message: str,
        error_code: str = "UNKNOWN_ERROR",
        status_code: int = 500,
        detail: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.detail = detail or {}
        super().__init__(self.message)


class ValidationError(CustomException):
    """数据验证异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=400,
            detail=detail
        )


class NotFoundError(CustomException):
    """资源不存在异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            status_code=404,
            detail=detail
        )


class DatabaseError(CustomException):
    """数据库操作异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            status_code=500,
            detail=detail
        )


class VectorDBError(CustomException):
    """向量数据库异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VECTOR_DB_ERROR",
            status_code=500,
            detail=detail
        )


class LLMError(CustomException):
    """大模型调用异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="LLM_ERROR",
            status_code=500,
            detail=detail
        )


class AuthenticationError(CustomException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=401,
            detail=detail
        )


class AuthorizationError(CustomException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=403,
            detail=detail
        )


class RateLimitError(CustomException):
    """频率限制异常"""
    
    def __init__(self, message: str = "请求过于频繁", detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="RATE_LIMIT_ERROR",
            status_code=429,
            detail=detail
        )


class ConfigurationError(CustomException):
    """配置错误异常"""
    
    def __init__(self, message: str, detail: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            status_code=500,
            detail=detail
        )
