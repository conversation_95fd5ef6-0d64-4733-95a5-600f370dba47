"""
服务层测试
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from app.services.qa_service import QAService
from app.services.vector_service import VectorService
from app.services.llm_service import LLMService
from app.models.schemas import QuestionRequest, VectorSearchResult, ResponseSource


class TestVectorService:
    """向量服务测试"""
    
    @pytest.mark.asyncio
    async def test_search_preset_answers_found(self):
        """测试搜索预设答案 - 找到结果"""
        vector_service = VectorService()
        
        # Mock客户端搜索方法
        mock_result = VectorSearchResult(
            id="1",
            content="要重置密码，请点击登录页面的'忘记密码'链接。",
            score=0.9,
            metadata={"category": "password"}
        )
        
        with patch.object(vector_service.client, 'search', return_value=[mock_result]):
            result = await vector_service.search_preset_answers("如何重置密码？")
            
            assert result is not None
            assert result.content == mock_result.content
            assert result.score == 0.9
    
    @pytest.mark.asyncio
    async def test_search_preset_answers_not_found(self):
        """测试搜索预设答案 - 未找到结果"""
        vector_service = VectorService()
        
        # Mock客户端搜索方法返回低分结果
        mock_result = VectorSearchResult(
            id="1",
            content="不相关的内容",
            score=0.5,  # 低于阈值
            metadata={}
        )
        
        with patch.object(vector_service.client, 'search', return_value=[mock_result]):
            result = await vector_service.search_preset_answers("如何重置密码？")
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_search_knowledge_base(self):
        """测试搜索知识库"""
        vector_service = VectorService()
        
        mock_results = [
            VectorSearchResult(
                id="1",
                content="密码重置相关文档1",
                score=0.8,
                metadata={"source": "doc1"}
            ),
            VectorSearchResult(
                id="2",
                content="密码重置相关文档2",
                score=0.75,
                metadata={"source": "doc2"}
            )
        ]
        
        with patch.object(vector_service.client, 'search', return_value=mock_results):
            results = await vector_service.search_knowledge_base("如何重置密码？")
            
            assert len(results) == 2
            assert all(result.score >= 0.7 for result in results)


class TestLLMService:
    """LLM服务测试"""
    
    @pytest.mark.asyncio
    async def test_openai_generate_answer(self):
        """测试OpenAI生成回答"""
        from app.services.llm_service import OpenAIClient
        
        client = OpenAIClient()
        
        # Mock API密钥获取
        with patch('app.services.llm_service.key_manager.get_api_key', return_value="test-key"):
            # Mock HTTP请求
            mock_response = MagicMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "根据提供的信息，您可以通过以下步骤重置密码..."
                        }
                    }
                ]
            }
            
            with patch('httpx.AsyncClient.post', return_value=mock_response):
                context_docs = [
                    VectorSearchResult(
                        id="1",
                        content="密码重置步骤说明",
                        score=0.8,
                        metadata={}
                    )
                ]
                
                answer = await client.generate_answer(
                    question="如何重置密码？",
                    context_documents=context_docs
                )
                
                assert "重置密码" in answer
                assert len(answer) > 0


class TestQAService:
    """问答服务测试"""
    
    @pytest.mark.asyncio
    async def test_process_question_preset_answer(self):
        """测试处理问题 - 预设答案流程"""
        qa_service = QAService()
        
        # Mock预设答案搜索返回结果
        mock_preset_result = VectorSearchResult(
            id="1",
            content="要重置密码，请访问设置页面。",
            score=0.9,
            metadata={}
        )
        
        with patch.object(qa_service.vector_service, 'search_preset_answers', return_value=mock_preset_result):
            with patch.object(qa_service, '_log_conversation', return_value=None):
                request = QuestionRequest(
                    question="如何重置密码？",
                    user_id="test_user"
                )
                
                response = await qa_service.process_question(request)
                
                assert response.answer == mock_preset_result.content
                assert response.source == ResponseSource.PRESET
                assert response.confidence == 0.9
                assert response.session_id is not None
    
    @pytest.mark.asyncio
    async def test_process_question_generated_answer(self):
        """测试处理问题 - 生成答案流程"""
        qa_service = QAService()
        
        # Mock预设答案搜索返回None
        # Mock知识库搜索返回结果
        mock_knowledge_results = [
            VectorSearchResult(
                id="1",
                content="密码重置文档内容",
                score=0.8,
                metadata={}
            )
        ]
        
        # Mock LLM生成回答
        mock_generated_answer = "根据文档，您可以通过以下步骤重置密码..."
        
        with patch.object(qa_service.vector_service, 'search_preset_answers', return_value=None):
            with patch.object(qa_service.vector_service, 'search_knowledge_base', return_value=mock_knowledge_results):
                with patch.object(qa_service.llm_service, 'generate_answer', return_value=mock_generated_answer):
                    with patch.object(qa_service, '_log_conversation', return_value=None):
                        request = QuestionRequest(
                            question="如何重置密码？",
                            user_id="test_user"
                        )
                        
                        response = await qa_service.process_question(request)
                        
                        assert response.answer == mock_generated_answer
                        assert response.source == ResponseSource.GENERATED
                        assert response.confidence == 0.8
                        assert len(response.vector_results) == 1
    
    @pytest.mark.asyncio
    async def test_process_question_no_results(self):
        """测试处理问题 - 无结果流程"""
        qa_service = QAService()
        
        # Mock所有搜索都返回空结果
        with patch.object(qa_service.vector_service, 'search_preset_answers', return_value=None):
            with patch.object(qa_service.vector_service, 'search_knowledge_base', return_value=[]):
                with patch.object(qa_service, '_log_conversation', return_value=None):
                    request = QuestionRequest(
                        question="一个完全无关的问题",
                        user_id="test_user"
                    )
                    
                    response = await qa_service.process_question(request)
                    
                    assert "没有找到相关的信息" in response.answer
                    assert response.source == ResponseSource.GENERATED
                    assert response.confidence == 0.0
                    assert len(response.vector_results) == 0
