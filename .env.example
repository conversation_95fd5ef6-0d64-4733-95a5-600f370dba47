# 应用配置
APP_NAME=智能客服系统
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=mysql+pymysql://user:password@localhost:3306/customer_service
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# 向量数据库配置
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=

# 第一个向量库（预设答案）
VECTOR_DB_1_COLLECTION=preset_answers
VECTOR_DB_1_SIMILARITY_THRESHOLD=0.85

# 第二个向量库（文档库）
VECTOR_DB_2_COLLECTION=knowledge_base
VECTOR_DB_2_SIMILARITY_THRESHOLD=0.7
VECTOR_DB_2_TOP_K=5

# 大模型配置
LLM_PROVIDER=openai  # openai, azure, anthropic
LLM_MODEL=gpt-3.5-turbo
LLM_MAX_TOKENS=1000
LLM_TEMPERATURE=0.7

# 安全配置
SECRET_KEY=your-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 日志配置
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5
LOG_FORMAT=json

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 测试配置
TEST_DATABASE_URL=mysql+pymysql://user:password@localhost:3306/customer_service_test
