#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.db.database import init_db, engine
from app.core.logging import setup_logging
import structlog

# 设置日志
setup_logging()
logger = structlog.get_logger(__name__)


async def main():
    """主函数"""
    try:
        logger.info("开始初始化数据库...")
        
        # 初始化数据库
        await init_db()
        
        logger.info("数据库初始化完成")
        
        # 可以在这里添加初始数据
        await insert_initial_data()
        
        logger.info("初始数据插入完成")
        
    except Exception as e:
        logger.error("数据库初始化失败", error=str(e))
        sys.exit(1)
    finally:
        # 关闭数据库连接
        await engine.dispose()


async def insert_initial_data():
    """插入初始数据"""
    from app.db.database import get_db_session
    from app.db.models import SystemConfig
    
    try:
        async with get_db_session() as session:
            # 插入系统配置
            configs = [
                SystemConfig(
                    config_key="system.version",
                    config_value="1.0.0",
                    config_type="string",
                    description="系统版本号"
                ),
                SystemConfig(
                    config_key="vector.default_threshold",
                    config_value="0.7",
                    config_type="float",
                    description="默认向量相似度阈值"
                ),
                SystemConfig(
                    config_key="llm.default_temperature",
                    config_value="0.7",
                    config_type="float",
                    description="默认LLM温度参数"
                )
            ]
            
            for config in configs:
                session.add(config)
            
            await session.commit()
            logger.info("系统配置插入完成", count=len(configs))
            
    except Exception as e:
        logger.error("插入初始数据失败", error=str(e))
        raise


if __name__ == "__main__":
    asyncio.run(main())
