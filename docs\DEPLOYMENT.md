# 部署指南

## 环境要求

- Python 3.11+
- MySQL 8.0+
- Redis 6.0+
- Qdrant 1.0+

## 本地开发环境

### 1. 克隆项目
```bash
git clone <repository-url>
cd customer_service
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
make install
# 或
pip install -r requirements.txt
```

### 4. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

### 5. 初始化数据库
```bash
make init-db
# 或
python scripts/init_db.py
```

### 6. 加载示例数据
```bash
make load-data
# 或
python scripts/load_sample_data.py
```

### 7. 启动服务
```bash
make run
# 或
uvicorn app.main:app --reload
```

## Docker部署

### 1. 使用Docker Compose（推荐）
```bash
# 启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

### 2. 单独构建镜像
```bash
# 构建镜像
docker build -t customer-service .

# 运行容器
docker run -p 8000:8000 customer-service
```

## 生产环境部署

### 1. 环境准备
- 确保所有依赖服务已安装并运行
- 配置防火墙和安全组
- 准备SSL证书（如需要）

### 2. 配置文件
```bash
# 生产环境配置
cp .env.example .env.production
# 编辑生产环境配置
```

### 3. 数据库配置
```sql
-- 创建数据库和用户
CREATE DATABASE customer_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'customer_service'@'%' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON customer_service.* TO 'customer_service'@'%';
FLUSH PRIVILEGES;
```

### 4. 启动服务
```bash
# 使用Gunicorn启动（推荐）
gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

# 或使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 5. 配置反向代理（Nginx）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 监控和日志

### 1. 日志配置
- 日志文件位置：`logs/app.log`
- 日志级别：通过环境变量 `LOG_LEVEL` 配置
- 日志格式：支持JSON和普通文本格式

### 2. 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查详细指标
curl http://localhost:8000/api/v1/health/metrics
```

### 3. 性能监控
- 使用Prometheus + Grafana监控系统指标
- 配置告警规则
- 监控关键业务指标

## 安全配置

### 1. 密钥管理
- 使用强密码和密钥
- 定期轮换API密钥
- 考虑使用外部密钥管理服务

### 2. 网络安全
- 配置防火墙规则
- 使用HTTPS
- 限制数据库访问

### 3. 应用安全
- 定期更新依赖
- 配置CORS策略
- 启用请求限流

## 备份和恢复

### 1. 数据库备份
```bash
# 备份MySQL数据库
mysqldump -u username -p customer_service > backup.sql

# 恢复数据库
mysql -u username -p customer_service < backup.sql
```

### 2. 向量数据库备份
- 定期备份Qdrant数据目录
- 配置自动备份脚本

### 3. 配置备份
- 备份环境变量配置
- 备份应用配置文件

## 故障排除

### 1. 常见问题
- 数据库连接失败：检查连接字符串和网络
- 向量搜索失败：检查Qdrant服务状态
- API调用失败：检查密钥配置

### 2. 日志分析
```bash
# 查看错误日志
grep "ERROR" logs/app.log

# 实时监控日志
tail -f logs/app.log
```

### 3. 性能问题
- 检查数据库查询性能
- 监控内存和CPU使用率
- 分析响应时间指标
