"""
SQLAlchemy数据库模型定义
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import String, Text, Boolean, Integer, Float, DateTime, Index
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy.sql import func

from app.db.database import Base


class ApiKey(Base):
    """API密钥表"""
    __tablename__ = "api_keys"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    provider: Mapped[str] = mapped_column(String(50), nullable=False, comment="提供商名称")
    key_type: Mapped[str] = mapped_column(String(50), nullable=False, default="default", comment="密钥类型")
    encrypted_value: Mapped[str] = mapped_column(Text, nullable=False, comment="加密后的密钥值")
    description: Mapped[Optional[str]] = mapped_column(String(255), comment="描述")
    is_active: Mapped[bool] = mapped_column(<PERSON>olean, default=True, comment="是否激活")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 创建复合索引
    __table_args__ = (
        Index('idx_provider_type', 'provider', 'key_type'),
        Index('idx_active', 'is_active'),
    )


class ConversationLog(Base):
    """对话日志表"""
    __tablename__ = "conversation_logs"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    session_id: Mapped[str] = mapped_column(String(100), nullable=False, comment="会话ID")
    user_id: Mapped[Optional[str]] = mapped_column(String(100), comment="用户ID")
    question: Mapped[str] = mapped_column(Text, nullable=False, comment="用户问题")
    answer: Mapped[str] = mapped_column(Text, nullable=False, comment="系统回答")
    source: Mapped[str] = mapped_column(String(20), nullable=False, comment="回答来源")
    confidence: Mapped[float] = mapped_column(Float, nullable=False, comment="置信度")
    response_time: Mapped[float] = mapped_column(Float, nullable=False, comment="响应时间（秒）")
    vector_results: Mapped[Optional[str]] = mapped_column(Text, comment="向量搜索结果JSON")
    llm_model: Mapped[Optional[str]] = mapped_column(String(100), comment="使用的LLM模型")
    processing_steps: Mapped[Optional[str]] = mapped_column(Text, comment="处理步骤JSON")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_session_id', 'session_id'),
        Index('idx_user_id', 'user_id'),
        Index('idx_source', 'source'),
        Index('idx_created_at', 'created_at'),
    )


class SystemConfig(Base):
    """系统配置表"""
    __tablename__ = "system_configs"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    config_key: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, comment="配置键")
    config_value: Mapped[str] = mapped_column(Text, nullable=False, comment="配置值")
    config_type: Mapped[str] = mapped_column(String(20), nullable=False, default="string", comment="配置类型")
    description: Mapped[Optional[str]] = mapped_column(String(255), comment="配置描述")
    is_encrypted: Mapped[bool] = mapped_column(Boolean, default=False, comment="是否加密存储")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_config_key', 'config_key'),
        Index('idx_active', 'is_active'),
    )


class VectorDocument(Base):
    """向量文档表（用于管理向量数据库中的文档）"""
    __tablename__ = "vector_documents"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    document_id: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, comment="文档ID")
    collection: Mapped[str] = mapped_column(String(50), nullable=False, comment="集合名称")
    title: Mapped[str] = mapped_column(String(255), nullable=False, comment="文档标题")
    content: Mapped[str] = mapped_column(Text, nullable=False, comment="文档内容")
    metadata: Mapped[Optional[str]] = mapped_column(Text, comment="元数据JSON")
    source: Mapped[Optional[str]] = mapped_column(String(255), comment="文档来源")
    category: Mapped[Optional[str]] = mapped_column(String(50), comment="文档分类")
    tags: Mapped[Optional[str]] = mapped_column(String(255), comment="标签")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, comment="是否激活")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    updated_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_document_id', 'document_id'),
        Index('idx_collection', 'collection'),
        Index('idx_category', 'category'),
        Index('idx_active', 'is_active'),
    )


class RequestMetrics(Base):
    """请求指标表"""
    __tablename__ = "request_metrics"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    endpoint: Mapped[str] = mapped_column(String(100), nullable=False, comment="API端点")
    method: Mapped[str] = mapped_column(String(10), nullable=False, comment="HTTP方法")
    status_code: Mapped[int] = mapped_column(Integer, nullable=False, comment="状态码")
    response_time: Mapped[float] = mapped_column(Float, nullable=False, comment="响应时间（秒）")
    user_id: Mapped[Optional[str]] = mapped_column(String(100), comment="用户ID")
    session_id: Mapped[Optional[str]] = mapped_column(String(100), comment="会话ID")
    error_message: Mapped[Optional[str]] = mapped_column(Text, comment="错误消息")
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now(), comment="创建时间")
    
    # 创建索引
    __table_args__ = (
        Index('idx_endpoint', 'endpoint'),
        Index('idx_status_code', 'status_code'),
        Index('idx_created_at', 'created_at'),
    )
