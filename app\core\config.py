"""
应用配置管理
支持从环境变量、配置文件等多种方式读取配置
"""
from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """应用设置类"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # 应用基础配置
    APP_NAME: str = Field(default="智能客服系统", description="应用名称")
    APP_VERSION: str = Field(default="1.0.0", description="应用版本")
    DEBUG: bool = Field(default=False, description="调试模式")
    LOG_LEVEL: str = Field(default="DEBUG", description="日志级别")
    
    # 服务器配置
    HOST: str = Field(default="0.0.0.0", description="服务器地址")
    PORT: int = Field(default=8000, description="服务器端口")
    
    # # 数据库配置
    # DATABASE_URL: str = Field(..., description="数据库连接URL")
    # DATABASE_POOL_SIZE: int = Field(default=10, description="数据库连接池大小")
    # DATABASE_MAX_OVERFLOW: int = Field(default=20, description="数据库连接池最大溢出")
    
    # Redis配置
    REDIS_URL: str = Field(default="redis://localhost:6379/0", description="Redis连接URL")
    REDIS_PASSWORD: Optional[str] = Field(default=None, description="Redis密码")
    REDIS_DB: int = Field(default=0, description="Redis数据库编号")
    
    # 向量数据库配置
    QDRANT_HOST: str = Field(default="localhost", description="Qdrant主机")
    QDRANT_PORT: int = Field(default=6333, description="Qdrant端口")
    QDRANT_API_KEY: Optional[str] = Field(default=None, description="Qdrant API密钥")
    
    # 第一个向量库配置（预设答案）
    VECTOR_DB_1_COLLECTION: str = Field(default="preset_answers", description="预设答案集合名")
    VECTOR_DB_1_SIMILARITY_THRESHOLD: float = Field(default=0.85, description="预设答案相似度阈值")
    
    # 第二个向量库配置（知识库）
    VECTOR_DB_2_COLLECTION: str = Field(default="knowledge_base", description="知识库集合名")
    VECTOR_DB_2_SIMILARITY_THRESHOLD: float = Field(default=0.7, description="知识库相似度阈值")
    VECTOR_DB_2_TOP_K: int = Field(default=5, description="知识库检索返回数量")
    
    # 大模型配置
    LLM_PROVIDER: str = Field(default="openai", description="LLM提供商")
    LLM_MODEL: str = Field(default="gpt-3.5-turbo", description="LLM模型名称")
    LLM_MAX_TOKENS: int = Field(default=1000, description="LLM最大token数")
    LLM_TEMPERATURE: float = Field(default=0.7, description="LLM温度参数")
    
    # # 安全配置
    # SECRET_KEY: str = Field(..., description="应用密钥")
    # ENCRYPTION_KEY: str = Field(..., description="加密密钥")
    # ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="访问令牌过期时间（分钟）")
    
    # 日志配置
    LOG_FILE_PATH: str = Field(default="logs/app.log", description="日志文件路径")
    LOG_MAX_SIZE: str = Field(default="10MB", description="日志文件最大大小")
    LOG_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")
    LOG_FORMAT: str = Field(default="json", description="日志格式")
    
    # 监控配置
    ENABLE_METRICS: bool = Field(default=True, description="启用指标监控")
    METRICS_PORT: int = Field(default=9090, description="指标监控端口")
    
    # 测试配置
    TEST_DATABASE_URL: Optional[str] = Field(default=None, description="测试数据库URL")
    
    # @property
    # def is_testing(self) -> bool:
    #     """判断是否为测试环境"""
    #     return self.TEST_DATABASE_URL is not None
    
    # @property
    # def database_url_for_env(self) -> str:
    #     """根据环境返回对应的数据库URL"""
    #     if self.is_testing and self.TEST_DATABASE_URL:
    #         return self.TEST_DATABASE_URL
    #     return self.DATABASE_URL


# 创建全局设置实例
settings = Settings()


class VectorDBConfig:
    """向量数据库配置类"""
    
    def __init__(self, collection: str, threshold: float, top_k: int = 1):
        self.collection = collection
        self.threshold = threshold
        self.top_k = top_k


# 向量数据库配置实例
vector_db_1_config = VectorDBConfig(
    collection=settings.VECTOR_DB_1_COLLECTION,
    threshold=settings.VECTOR_DB_1_SIMILARITY_THRESHOLD,
    top_k=1
)

vector_db_2_config = VectorDBConfig(
    collection=settings.VECTOR_DB_2_COLLECTION,
    threshold=settings.VECTOR_DB_2_SIMILARITY_THRESHOLD,
    top_k=settings.VECTOR_DB_2_TOP_K
)
