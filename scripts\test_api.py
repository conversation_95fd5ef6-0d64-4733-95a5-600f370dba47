#!/usr/bin/env python3
"""
API测试脚本
用于快速测试API功能
"""
import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import httpx
from app.core.config import settings


class APITester:
    """API测试器"""
    
    def __init__(self, base_url: str = None):
        self.base_url = base_url or f"http://{settings.HOST}:{settings.PORT}"
        self.client = None
    
    async def __aenter__(self):
        self.client = httpx.AsyncClient(base_url=self.base_url, timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.aclose()
    
    async def test_health(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        
        try:
            # 测试ping
            response = await self.client.get("/api/v1/health/ping")
            print(f"  Ping: {response.status_code} - {response.json()}")
            
            # 测试健康检查
            response = await self.client.get("/api/v1/health/")
            print(f"  Health: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"    状态: {data['status']}")
                print(f"    组件: {data['components']}")
            
            # 测试指标
            response = await self.client.get("/api/v1/health/metrics")
            print(f"  Metrics: {response.status_code}")
            
        except Exception as e:
            print(f"  ❌ 健康检查测试失败: {e}")
    
    async def test_qa(self):
        """测试问答功能"""
        print("🤖 测试问答功能...")
        
        test_questions = [
            {
                "question": "如何重置密码？",
                "user_id": "test_user_1",
                "question_type": "general"
            },
            {
                "question": "系统支持哪些功能？",
                "user_id": "test_user_2",
                "question_type": "technical"
            }
        ]
        
        for i, question_data in enumerate(test_questions, 1):
            try:
                print(f"  测试问题 {i}: {question_data['question']}")
                
                response = await self.client.post("/api/v1/qa/ask", json=question_data)
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"    回答: {data['answer'][:100]}...")
                    print(f"    来源: {data['source']}")
                    print(f"    置信度: {data['confidence']}")
                    print(f"    响应时间: {data['response_time']:.3f}s")
                    
                    # 测试获取对话历史
                    if 'session_id' in data:
                        history_response = await self.client.get(
                            f"/api/v1/qa/history/{data['session_id']}"
                        )
                        print(f"    历史记录: {history_response.status_code}")
                else:
                    print(f"    ❌ 错误: {response.text}")
                
                print()
                
            except Exception as e:
                print(f"    ❌ 问答测试失败: {e}")
    
    async def test_vector_search(self):
        """测试向量搜索"""
        print("🔍 测试向量搜索...")
        
        search_requests = [
            {
                "query": "密码重置",
                "collection": "preset_answers",
                "top_k": 3,
                "threshold": 0.7
            },
            {
                "query": "系统功能",
                "collection": "knowledge_base",
                "top_k": 5,
                "threshold": 0.6
            }
        ]
        
        for i, search_data in enumerate(search_requests, 1):
            try:
                print(f"  搜索 {i}: {search_data['query']} (集合: {search_data['collection']})")
                
                response = await self.client.post("/api/v1/qa/search", json=search_data)
                print(f"    状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    print(f"    结果数量: {data['total']}")
                    for j, result in enumerate(data['results'][:2]):  # 只显示前2个结果
                        print(f"      结果 {j+1}: 分数={result['score']:.3f}")
                        print(f"        内容: {result['content'][:50]}...")
                else:
                    print(f"    ❌ 错误: {response.text}")
                
                print()
                
            except Exception as e:
                print(f"    ❌ 向量搜索测试失败: {e}")
    
    async def test_admin(self):
        """测试管理功能"""
        print("⚙️ 测试管理功能...")
        
        try:
            # 测试获取API密钥列表
            response = await self.client.get("/api/v1/admin/api-keys")
            print(f"  API密钥列表: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"    密钥数量: {len(data)}")
            
            # 测试清空缓存
            response = await self.client.post("/api/v1/admin/cache/clear")
            print(f"  清空缓存: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"    结果: {data['message']}")
            
        except Exception as e:
            print(f"  ❌ 管理功能测试失败: {e}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print(f"🚀 开始API测试 (服务器: {self.base_url})")
        print("=" * 50)
        
        await self.test_health()
        print()
        
        await self.test_qa()
        print()
        
        await self.test_vector_search()
        print()
        
        await self.test_admin()
        print()
        
        print("✅ API测试完成")


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="API测试脚本")
    parser.add_argument(
        "--url",
        default=f"http://{settings.HOST}:{settings.PORT}",
        help="API服务器URL"
    )
    parser.add_argument(
        "--test",
        choices=["health", "qa", "vector", "admin", "all"],
        default="all",
        help="要运行的测试类型"
    )
    
    args = parser.parse_args()
    
    async with APITester(args.url) as tester:
        if args.test == "health":
            await tester.test_health()
        elif args.test == "qa":
            await tester.test_qa()
        elif args.test == "vector":
            await tester.test_vector_search()
        elif args.test == "admin":
            await tester.test_admin()
        else:
            await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
