from app.core.logging import setup_logging, api_logger, db_logger, llm_logger

# 初始化日志配置
setup_logging()

def test_loggers():
    # 测试不同日志级别
    api_logger.debug("API调试信息")    # 应不可见（默认级别INFO）
    api_logger.info("API接口请求", path="/users", method="GET")
    db_logger.warning("数据库连接延迟", delay_ms=450)
    
    # 测试异常堆栈
    try:
        1 / 0
    except Exception:
        llm_logger.error("模型推理异常", exc_info=True)
    
    # 测试上下文绑定
    logger = api_logger.bind(user_id=123)
    logger.info("用户操作日志", action="login")
