"""
问答服务 - 核心业务逻辑
实现双重检索 + LLM生成的完整流程
"""
import time
import uuid
from typing import Optional, List
import structlog

from app.core.logging import LoggerMixin
from app.models.schemas import (
    QuestionRequest, QuestionResponse, ResponseSource,
    VectorSearchResult
)
from app.services.vector_service import vector_service
from app.services.llm_service import llm_service
from app.db.database import get_db_session
from app.db.models import ConversationLog

logger = structlog.get_logger(__name__)


class QAService(LoggerMixin):
    """问答服务类"""
    
    def __init__(self):
        self.vector_service = vector_service
        self.llm_service = llm_service
    
    async def process_question(self, request: QuestionRequest) -> QuestionResponse:
        """
        处理用户问题的主要流程
        
        Args:
            request: 问题请求
            
        Returns:
            问题回答响应
        """
        start_time = time.time()
        session_id = request.session_id or str(uuid.uuid4())
        processing_steps = []
        
        try:
            self.logger.info(
                "开始处理问题",
                question=request.question[:100],  # 只记录前100个字符
                user_id=request.user_id,
                session_id=session_id
            )
            
            # 第一步：搜索预设答案
            processing_steps.append("搜索预设答案")
            preset_result = await self.vector_service.search_preset_answers(request.question)
            
            if preset_result:
                # 找到预设答案，直接返回
                processing_steps.append("找到预设答案")
                response_time = time.time() - start_time
                
                response = QuestionResponse(
                    answer=preset_result.content,
                    source=ResponseSource.PRESET,
                    confidence=preset_result.score,
                    response_time=response_time,
                    session_id=session_id,
                    vector_results=[preset_result],
                    processing_steps=processing_steps
                )
                
                # 记录对话日志
                await self._log_conversation(request, response)
                
                self.logger.info(
                    "预设答案处理完成",
                    session_id=session_id,
                    confidence=preset_result.score,
                    response_time=response_time
                )
                
                return response
            
            # 第二步：搜索知识库
            processing_steps.append("搜索知识库")
            knowledge_results = await self.vector_service.search_knowledge_base(request.question)
            
            if not knowledge_results:
                # 没有找到相关文档
                processing_steps.append("未找到相关文档")
                response_time = time.time() - start_time
                
                response = QuestionResponse(
                    answer="抱歉，我没有找到相关的信息来回答您的问题。请您提供更多详细信息或联系人工客服。",
                    source=ResponseSource.GENERATED,
                    confidence=0.0,
                    response_time=response_time,
                    session_id=session_id,
                    vector_results=[],
                    processing_steps=processing_steps
                )
                
                await self._log_conversation(request, response)
                
                self.logger.info(
                    "未找到相关文档",
                    session_id=session_id,
                    response_time=response_time
                )
                
                return response
            
            # 第三步：使用LLM生成回答
            processing_steps.append("LLM生成回答")
            generated_answer = await self.llm_service.generate_answer(
                question=request.question,
                context_documents=knowledge_results
            )
            
            # 计算置信度（基于检索结果的平均分数）
            confidence = sum(result.score for result in knowledge_results) / len(knowledge_results)
            
            response_time = time.time() - start_time
            processing_steps.append("回答生成完成")
            
            response = QuestionResponse(
                answer=generated_answer,
                source=ResponseSource.GENERATED,
                confidence=confidence,
                response_time=response_time,
                session_id=session_id,
                vector_results=knowledge_results,
                llm_model=self.llm_service.provider,
                processing_steps=processing_steps
            )
            
            # 记录对话日志
            await self._log_conversation(request, response)
            
            self.logger.info(
                "LLM回答生成完成",
                session_id=session_id,
                confidence=confidence,
                response_time=response_time,
                context_docs=len(knowledge_results)
            )
            
            return response
            
        except Exception as e:
            response_time = time.time() - start_time
            processing_steps.append(f"处理失败: {str(e)}")
            
            self.logger.error(
                "问题处理失败",
                session_id=session_id,
                error=str(e),
                response_time=response_time,
                exc_info=True
            )
            
            # 返回错误响应
            error_response = QuestionResponse(
                answer="抱歉，系统暂时无法处理您的问题，请稍后重试或联系人工客服。",
                source=ResponseSource.GENERATED,
                confidence=0.0,
                response_time=response_time,
                session_id=session_id,
                processing_steps=processing_steps
            )
            
            try:
                await self._log_conversation(request, error_response)
            except Exception as log_error:
                self.logger.error("记录对话日志失败", error=str(log_error))
            
            return error_response
    
    async def _log_conversation(
        self,
        request: QuestionRequest,
        response: QuestionResponse
    ) -> None:
        """记录对话日志到数据库"""
        try:
            async with get_db_session() as session:
                log_entry = ConversationLog(
                    session_id=response.session_id,
                    user_id=request.user_id,
                    question=request.question,
                    answer=response.answer,
                    source=response.source.value,
                    confidence=response.confidence,
                    response_time=response.response_time,
                    vector_results=self._serialize_vector_results(response.vector_results),
                    llm_model=response.llm_model,
                    processing_steps=",".join(response.processing_steps or [])
                )
                
                session.add(log_entry)
                await session.commit()
                
                self.logger.debug("对话日志记录成功", session_id=response.session_id)
                
        except Exception as e:
            self.logger.error("记录对话日志失败", error=str(e))
            # 不抛出异常，避免影响主流程
    
    def _serialize_vector_results(self, results: Optional[List[VectorSearchResult]]) -> Optional[str]:
        """序列化向量搜索结果"""
        if not results:
            return None
        
        try:
            import json
            serialized_results = []
            for result in results:
                serialized_results.append({
                    "id": result.id,
                    "content": result.content[:500],  # 限制长度
                    "score": result.score,
                    "metadata": result.metadata
                })
            return json.dumps(serialized_results, ensure_ascii=False)
        except Exception as e:
            self.logger.error("序列化向量结果失败", error=str(e))
            return None
    
    async def get_conversation_history(
        self,
        session_id: str,
        limit: int = 10
    ) -> List[dict]:
        """获取对话历史"""
        try:
            async with get_db_session() as session:
                from sqlalchemy import select, desc
                
                stmt = (
                    select(ConversationLog)
                    .where(ConversationLog.session_id == session_id)
                    .order_by(desc(ConversationLog.created_at))
                    .limit(limit)
                )
                
                result = await session.execute(stmt)
                logs = result.scalars().all()
                
                history = []
                for log in reversed(logs):  # 按时间正序返回
                    history.append({
                        "question": log.question,
                        "answer": log.answer,
                        "source": log.source,
                        "confidence": log.confidence,
                        "created_at": log.created_at.isoformat()
                    })
                
                return history
                
        except Exception as e:
            self.logger.error("获取对话历史失败", session_id=session_id, error=str(e))
            return []


# 全局问答服务实例
qa_service = QAService()
