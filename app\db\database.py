"""
数据库连接和会话管理
"""
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
import structlog

from app.core.config import settings
from app.core.exceptions import DatabaseError

logger = structlog.get_logger(__name__)


class Base(DeclarativeBase):
    """SQLAlchemy基础模型类"""
    pass


# 创建异步数据库引擎
engine = create_async_engine(
    settings.database_url_for_env,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    echo=settings.DEBUG,  # 开发环境显示SQL语句
    future=True
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)


async def init_db() -> None:
    """初始化数据库"""
    try:
        # 导入所有模型以确保它们被注册
        from app.db import models  # noqa
        
        # 创建所有表
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error("数据库初始化失败", error=str(e))
        raise DatabaseError(f"数据库初始化失败: {e}")


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话上下文管理器"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception as e:
            await session.rollback()
            logger.error("数据库操作失败", error=str(e))
            raise DatabaseError(f"数据库操作失败: {e}")
        finally:
            await session.close()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI依赖注入用的数据库会话获取器"""
    async with get_db_session() as session:
        yield session


async def close_db() -> None:
    """关闭数据库连接"""
    await engine.dispose()
    logger.info("数据库连接已关闭")
