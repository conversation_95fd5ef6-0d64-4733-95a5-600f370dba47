# 智能客服系统 Makefile

.PHONY: help install dev test lint format clean run init-db load-data

# 默认目标
help:
	@echo "可用命令:"
	@echo "  install     - 安装依赖"
	@echo "  dev         - 安装开发依赖"
	@echo "  test        - 运行测试"
	@echo "  lint        - 代码检查"
	@echo "  format      - 代码格式化"
	@echo "  clean       - 清理临时文件"
	@echo "  run         - 启动服务"
	@echo "  init-db     - 初始化数据库"
	@echo "  load-data   - 加载示例数据"
	@echo "  test-api    - 测试API接口"

# 安装依赖
install:
	pip install -r requirements.txt

# 安装开发依赖
dev: install
	pip install pytest pytest-asyncio pytest-cov black isort flake8 mypy

# 运行测试
test:
	pytest

# 运行测试并生成覆盖率报告
test-cov:
	pytest --cov=app --cov-report=html --cov-report=term

# 代码检查
lint:
	flake8 app tests scripts
	mypy app

# 代码格式化
format:
	black app tests scripts
	isort app tests scripts

# 清理临时文件
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf dist
	rm -rf build

# 启动服务
run:
	uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动生产服务
run-prod:
	uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4

# 初始化数据库
init-db:
	python scripts/init_db.py

# 加载示例数据
load-data:
	python scripts/load_sample_data.py

# 测试API接口
test-api:
	python scripts/test_api.py

# 检查代码质量
quality: format lint test

# 完整的开发环境设置
setup: dev init-db load-data
	@echo "开发环境设置完成!"
	@echo "运行 'make run' 启动服务"

# Docker相关命令
docker-build:
	docker build -t customer-service .

docker-run:
	docker run -p 8000:8000 customer-service

# 数据库迁移（如果使用Alembic）
migrate:
	alembic upgrade head

# 创建新的迁移
migration:
	alembic revision --autogenerate -m "$(msg)"
