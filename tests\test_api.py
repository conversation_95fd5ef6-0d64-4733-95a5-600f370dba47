"""
API接口测试
"""
import pytest
from httpx import AsyncClient


class TestHealthAPI:
    """健康检查API测试"""
    
    @pytest.mark.asyncio
    async def test_ping(self, client: AsyncClient):
        """测试ping接口"""
        response = await client.get("/api/v1/health/ping")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "pong"
        assert "timestamp" in data
        assert "version" in data
    
    @pytest.mark.asyncio
    async def test_health_check(self, client: AsyncClient):
        """测试健康检查接口"""
        response = await client.get("/api/v1/health/")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
        assert "components" in data


class TestQAAPI:
    """问答API测试"""
    
    @pytest.mark.asyncio
    async def test_ask_question_basic(self, client: AsyncClient, sample_question_request):
        """测试基本问答功能"""
        response = await client.post("/api/v1/qa/ask", json=sample_question_request)
        
        # 由于没有实际的向量数据库和LLM，这里可能会返回错误
        # 但我们可以测试API结构是否正确
        assert response.status_code in [200, 500]  # 允许内部错误
        
        if response.status_code == 200:
            data = response.json()
            assert "answer" in data
            assert "source" in data
            assert "confidence" in data
            assert "response_time" in data
            assert "session_id" in data
    
    @pytest.mark.asyncio
    async def test_ask_question_validation(self, client: AsyncClient):
        """测试问答请求验证"""
        # 测试空问题
        response = await client.post("/api/v1/qa/ask", json={"question": ""})
        assert response.status_code == 422  # 验证错误
        
        # 测试缺少问题字段
        response = await client.post("/api/v1/qa/ask", json={})
        assert response.status_code == 422  # 验证错误
    
    @pytest.mark.asyncio
    async def test_get_conversation_history(self, client: AsyncClient):
        """测试获取对话历史"""
        session_id = "test_session_123"
        response = await client.get(f"/api/v1/qa/history/{session_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_vector_search(self, client: AsyncClient, sample_vector_search_request):
        """测试向量搜索接口"""
        response = await client.post("/api/v1/qa/search", json=sample_vector_search_request)
        
        # 由于没有实际的向量数据库，这里可能会返回错误
        assert response.status_code in [200, 500]  # 允许内部错误
        
        if response.status_code == 200:
            data = response.json()
            assert "results" in data
            assert "total" in data
            assert "query" in data
            assert "collection" in data


class TestAdminAPI:
    """管理API测试"""
    
    @pytest.mark.asyncio
    async def test_create_api_key(self, client: AsyncClient, sample_api_key_request):
        """测试创建API密钥"""
        response = await client.post("/api/v1/admin/api-keys", json=sample_api_key_request)
        
        # 由于数据库操作可能失败，允许多种状态码
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "message" in data
            assert data["provider"] == sample_api_key_request["provider"]
    
    @pytest.mark.asyncio
    async def test_list_api_keys(self, client: AsyncClient):
        """测试获取API密钥列表"""
        response = await client.get("/api/v1/admin/api-keys")
        
        assert response.status_code in [200, 500]  # 允许数据库错误
        
        if response.status_code == 200:
            data = response.json()
            assert isinstance(data, list)
    
    @pytest.mark.asyncio
    async def test_clear_cache(self, client: AsyncClient):
        """测试清空缓存"""
        response = await client.post("/api/v1/admin/cache/clear")
        
        assert response.status_code in [200, 500]
        
        if response.status_code == 200:
            data = response.json()
            assert "message" in data


class TestRootAPI:
    """根路径API测试"""
    
    @pytest.mark.asyncio
    async def test_root(self, client: AsyncClient):
        """测试根路径"""
        response = await client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "docs" in data
    
    @pytest.mark.asyncio
    async def test_health_endpoint(self, client: AsyncClient):
        """测试健康检查端点"""
        response = await client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data
