"""
健康检查相关API端点
"""
import time
from datetime import datetime
from fastapi import APIRouter, HTTPException
import structlog

from app.core.config import settings
from app.models.schemas import HealthCheckResponse, MetricsResponse
from app.db.database import engine

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.get(
    "/",
    response_model=HealthCheckResponse,
    summary="健康检查",
    description="检查系统各组件的健康状态"
)
async def health_check() -> HealthCheckResponse:
    """
    系统健康检查
    
    检查以下组件：
    - 数据库连接
    - 向量数据库连接
    - Redis连接（如果配置）
    """
    components = {}
    
    # 检查数据库连接
    try:
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        components["database"] = "healthy"
    except Exception as e:
        logger.error("数据库健康检查失败", error=str(e))
        components["database"] = f"unhealthy: {str(e)}"
    
    # 检查向量数据库连接
    try:
        from app.services.vector_service import vector_service
        # 这里可以添加向量数据库的健康检查
        # await vector_service.client._get_client()
        components["vector_db"] = "healthy"
    except Exception as e:
        logger.error("向量数据库健康检查失败", error=str(e))
        components["vector_db"] = f"unhealthy: {str(e)}"
    
    # 检查Redis连接（如果配置了Redis）
    try:
        # 这里可以添加Redis的健康检查
        components["redis"] = "not_configured"
    except Exception as e:
        logger.error("Redis健康检查失败", error=str(e))
        components["redis"] = f"unhealthy: {str(e)}"
    
    # 确定整体状态
    overall_status = "healthy" if all(
        status == "healthy" or status == "not_configured" 
        for status in components.values()
    ) else "unhealthy"
    
    return HealthCheckResponse(
        status=overall_status,
        timestamp=datetime.now(),
        version=settings.APP_VERSION,
        components=components
    )


@router.get(
    "/metrics",
    response_model=MetricsResponse,
    summary="系统指标",
    description="获取系统运行指标和统计信息"
)
async def get_metrics() -> MetricsResponse:
    """
    获取系统指标
    
    包括：
    - 请求统计
    - 响应时间统计
    - 答案来源统计
    """
    try:
        from app.db.database import get_db_session
        from app.db.models import ConversationLog, RequestMetrics
        from sqlalchemy import select, func
        
        async with get_db_session() as session:
            # 获取对话统计
            conversation_stats = await session.execute(
                select(
                    func.count(ConversationLog.id).label("total_conversations"),
                    func.avg(ConversationLog.response_time).label("avg_response_time"),
                    func.sum(
                        func.case((ConversationLog.source == "preset", 1), else_=0)
                    ).label("preset_answers"),
                    func.sum(
                        func.case((ConversationLog.source == "generated", 1), else_=0)
                    ).label("generated_answers")
                )
            )
            
            stats = conversation_stats.first()
            
            total_conversations = stats.total_conversations or 0
            avg_response_time = float(stats.avg_response_time or 0)
            preset_answers = stats.preset_answers or 0
            generated_answers = stats.generated_answers or 0
            
            # 计算比率
            preset_rate = (preset_answers / total_conversations * 100) if total_conversations > 0 else 0
            generated_rate = (generated_answers / total_conversations * 100) if total_conversations > 0 else 0
            
            # 获取请求统计
            request_stats = await session.execute(
                select(
                    func.count(RequestMetrics.id).label("total_requests"),
                    func.sum(
                        func.case((RequestMetrics.status_code < 400, 1), else_=0)
                    ).label("successful_requests"),
                    func.sum(
                        func.case((RequestMetrics.status_code >= 400, 1), else_=0)
                    ).label("failed_requests")
                )
            )
            
            req_stats = request_stats.first()
            
            return MetricsResponse(
                total_requests=req_stats.total_requests or 0,
                successful_requests=req_stats.successful_requests or 0,
                failed_requests=req_stats.failed_requests or 0,
                average_response_time=avg_response_time,
                preset_answer_rate=preset_rate,
                generated_answer_rate=generated_rate,
                timestamp=datetime.now()
            )
            
    except Exception as e:
        logger.error("获取系统指标失败", error=str(e))
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "METRICS_ERROR",
                "message": "获取系统指标失败",
                "detail": str(e)
            }
        )


@router.get(
    "/ping",
    summary="简单ping检查",
    description="最简单的存活检查"
)
async def ping():
    """简单的ping检查"""
    return {
        "message": "pong",
        "timestamp": time.time(),
        "version": settings.APP_VERSION
    }
