"""
向量数据库服务
支持双重向量检索策略
"""
from typing import List, Optional, Dict, Any
import asyncio
from abc import ABC, abstractmethod
import structlog

from app.core.config import settings, vector_db_1_config, vector_db_2_config
from app.core.exceptions import VectorDBError
from app.models.schemas import VectorSearchResult, VectorSearchResponse

logger = structlog.get_logger(__name__)


class VectorDBClient(ABC):
    """向量数据库客户端抽象基类"""
    
    @abstractmethod
    async def search(
        self,
        query: str,
        collection: str,
        top_k: int = 5,
        threshold: float = 0.7
    ) -> List[VectorSearchResult]:
        """搜索向量"""
        pass
    
    @abstractmethod
    async def insert(
        self,
        collection: str,
        documents: List[Dict[str, Any]]
    ) -> bool:
        """插入文档"""
        pass
    
    @abstractmethod
    async def delete(
        self,
        collection: str,
        document_ids: List[str]
    ) -> bool:
        """删除文档"""
        pass


class QdrantClient(VectorDBClient):
    """Qdrant向量数据库客户端"""
    
    def __init__(self):
        self.host = settings.QDRANT_HOST
        self.port = settings.QDRANT_PORT
        self.api_key = settings.QDRANT_API_KEY
        self._client = None
    
    async def _get_client(self):
        """获取Qdrant客户端"""
        if self._client is None:
            try:
                from qdrant_client import AsyncQdrantClient
                self._client = AsyncQdrantClient(
                    host=self.host,
                    port=self.port,
                    api_key=self.api_key
                )
                logger.info("Qdrant客户端连接成功", host=self.host, port=self.port)
            except Exception as e:
                logger.error("Qdrant客户端连接失败", error=str(e))
                raise VectorDBError(f"Qdrant连接失败: {e}")
        return self._client
    
    async def search(
        self,
        query: str,
        collection: str,
        top_k: int = 5,
        threshold: float = 0.7
    ) -> List[VectorSearchResult]:
        """搜索向量"""
        try:
            client = await self._get_client()
            
            # 这里需要实现向量化查询文本的逻辑
            # 示例：使用sentence-transformers
            query_vector = await self._encode_text(query)
            
            # 执行搜索
            search_result = await client.search(
                collection_name=collection,
                query_vector=query_vector,
                limit=top_k,
                score_threshold=threshold
            )
            
            # 转换结果格式
            results = []
            for point in search_result:
                if point.score >= threshold:
                    result = VectorSearchResult(
                        id=str(point.id),
                        content=point.payload.get("content", ""),
                        score=point.score,
                        metadata=point.payload
                    )
                    results.append(result)
            
            logger.info(
                "向量搜索完成",
                collection=collection,
                query_length=len(query),
                results_count=len(results)
            )
            
            return results
            
        except Exception as e:
            logger.error("向量搜索失败", collection=collection, error=str(e))
            raise VectorDBError(f"向量搜索失败: {e}")
    
    async def _encode_text(self, text: str) -> List[float]:
        """将文本编码为向量"""
        try:
            # 这里应该使用实际的向量化模型
            # 示例实现：
            from sentence_transformers import SentenceTransformer
            
            # 加载模型（实际应用中应该缓存模型）
            model = SentenceTransformer('all-MiniLM-L6-v2')
            vector = model.encode(text).tolist()
            
            return vector
            
        except Exception as e:
            logger.error("文本向量化失败", error=str(e))
            raise VectorDBError(f"文本向量化失败: {e}")
    
    async def insert(
        self,
        collection: str,
        documents: List[Dict[str, Any]]
    ) -> bool:
        """插入文档"""
        try:
            client = await self._get_client()
            
            # 准备插入数据
            points = []
            for doc in documents:
                vector = await self._encode_text(doc["content"])
                point = {
                    "id": doc["id"],
                    "vector": vector,
                    "payload": doc
                }
                points.append(point)
            
            # 执行插入
            await client.upsert(
                collection_name=collection,
                points=points
            )
            
            logger.info("文档插入成功", collection=collection, count=len(documents))
            return True
            
        except Exception as e:
            logger.error("文档插入失败", collection=collection, error=str(e))
            return False
    
    async def delete(
        self,
        collection: str,
        document_ids: List[str]
    ) -> bool:
        """删除文档"""
        try:
            client = await self._get_client()
            
            await client.delete(
                collection_name=collection,
                points_selector=document_ids
            )
            
            logger.info("文档删除成功", collection=collection, count=len(document_ids))
            return True
            
        except Exception as e:
            logger.error("文档删除失败", collection=collection, error=str(e))
            return False


class VectorService:
    """向量服务 - 实现双重检索策略"""
    
    def __init__(self):
        self.client = QdrantClient()
        self.preset_config = vector_db_1_config
        self.knowledge_config = vector_db_2_config
    
    async def search_preset_answers(self, query: str) -> Optional[VectorSearchResult]:
        """
        第一步：搜索预设答案
        
        Args:
            query: 用户问题
            
        Returns:
            如果找到高相似度的预设答案则返回，否则返回None
        """
        try:
            results = await self.client.search(
                query=query,
                collection=self.preset_config.collection,
                top_k=1,
                threshold=self.preset_config.threshold
            )
            
            if results and results[0].score >= self.preset_config.threshold:
                logger.info(
                    "找到预设答案",
                    query_length=len(query),
                    score=results[0].score,
                    threshold=self.preset_config.threshold
                )
                return results[0]
            
            logger.info(
                "未找到合适的预设答案",
                query_length=len(query),
                threshold=self.preset_config.threshold
            )
            return None
            
        except Exception as e:
            logger.error("预设答案搜索失败", error=str(e))
            # 不抛出异常，继续后续流程
            return None
    
    async def search_knowledge_base(self, query: str) -> List[VectorSearchResult]:
        """
        第二步：搜索知识库
        
        Args:
            query: 用户问题
            
        Returns:
            相关文档列表
        """
        try:
            results = await self.client.search(
                query=query,
                collection=self.knowledge_config.collection,
                top_k=self.knowledge_config.top_k,
                threshold=self.knowledge_config.threshold
            )
            
            # 过滤低分结果
            filtered_results = [
                result for result in results
                if result.score >= self.knowledge_config.threshold
            ]
            
            logger.info(
                "知识库搜索完成",
                query_length=len(query),
                total_results=len(results),
                filtered_results=len(filtered_results),
                threshold=self.knowledge_config.threshold
            )
            
            return filtered_results
            
        except Exception as e:
            logger.error("知识库搜索失败", error=str(e))
            raise VectorDBError(f"知识库搜索失败: {e}")
    
    async def dual_search(self, query: str) -> tuple[Optional[VectorSearchResult], List[VectorSearchResult]]:
        """
        执行双重搜索策略
        
        Args:
            query: 用户问题
            
        Returns:
            (预设答案结果, 知识库搜索结果)
        """
        # 并发执行两个搜索
        preset_task = self.search_preset_answers(query)
        knowledge_task = self.search_knowledge_base(query)
        
        preset_result, knowledge_results = await asyncio.gather(
            preset_task,
            knowledge_task,
            return_exceptions=True
        )
        
        # 处理异常
        if isinstance(preset_result, Exception):
            logger.error("预设答案搜索异常", error=str(preset_result))
            preset_result = None
        
        if isinstance(knowledge_results, Exception):
            logger.error("知识库搜索异常", error=str(knowledge_results))
            knowledge_results = []
        
        return preset_result, knowledge_results


# 全局向量服务实例
vector_service = VectorService()
