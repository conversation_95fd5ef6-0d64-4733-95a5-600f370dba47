# 智能客服系统

## 项目概述

这是一个基于向量检索和大模型的智能客服系统，支持双重向量检索策略：
1. 首先检索高相似度的预设答案
2. 如果没有匹配，则检索相关文档并通过LLM生成答案

## 架构特点

- **分层架构**: 清晰的分层设计，便于维护和扩展
- **异步处理**: 基于FastAPI的高性能异步服务
- **双重检索**: 预设答案 + 文档检索的混合策略
- **安全管理**: 密钥加密存储和安全的配置管理
- **可观测性**: 完整的日志记录和监控体系
- **测试覆盖**: 全面的单元测试和集成测试

## 项目结构

```
customer_service/
├── app/                    # 应用主目录
│   ├── api/               # API路由
│   ├── core/              # 核心配置和工具
│   ├── services/          # 业务逻辑服务
│   ├── models/            # 数据模型
│   ├── db/                # 数据库相关
│   └── utils/             # 工具函数
├── tests/                 # 测试目录
├── scripts/               # 脚本目录
├── configs/               # 配置文件
├── logs/                  # 日志目录
└── docs/                  # 文档目录
```

## 快速开始

1. 安装依赖：`pip install -r requirements.txt`
2. 配置环境变量：复制 `.env.example` 到 `.env`
3. 初始化数据库：`python scripts/init_db.py`
4. 启动服务：`uvicorn app.main:app --reload`

## 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=app --cov-report=html
```

## API文档

启动服务后访问：http://localhost:8000/docs
