"""
问答相关API端点
"""
from typing import List
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
import structlog

from app.models.schemas import (
    QuestionRequest, QuestionResponse, ErrorResponse,
    VectorSearchRequest, VectorSearchResponse
)
from app.services.qa_service import qa_service
from app.services.vector_service import vector_service
from app.core.exceptions import CustomException

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post(
    "/ask",
    response_model=QuestionResponse,
    summary="提问接口",
    description="用户提问的主要接口，支持双重向量检索和LLM生成回答"
)
async def ask_question(request: QuestionRequest) -> QuestionResponse:
    """
    处理用户问题
    
    - **question**: 用户问题（必填）
    - **user_id**: 用户ID（可选）
    - **session_id**: 会话ID（可选，如果不提供会自动生成）
    - **question_type**: 问题类型（可选）
    - **context**: 上下文信息（可选）
    
    返回包含答案、来源、置信度等信息的响应
    """
    try:
        logger.info(
            "收到问答请求",
            question_length=len(request.question),
            user_id=request.user_id,
            session_id=request.session_id
        )
        
        response = await qa_service.process_question(request)
        
        logger.info(
            "问答请求处理完成",
            session_id=response.session_id,
            source=response.source,
            confidence=response.confidence,
            response_time=response.response_time
        )
        
        return response
        
    except CustomException as e:
        logger.error(
            "问答请求处理失败",
            error_code=e.error_code,
            message=e.message,
            detail=e.detail
        )
        raise HTTPException(
            status_code=e.status_code,
            detail={
                "error_code": e.error_code,
                "message": e.message,
                "detail": e.detail
            }
        )
    except Exception as e:
        logger.error("问答请求处理异常", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "INTERNAL_ERROR",
                "message": "系统内部错误",
                "detail": str(e)
            }
        )


@router.get(
    "/history/{session_id}",
    response_model=List[dict],
    summary="获取对话历史",
    description="根据会话ID获取对话历史记录"
)
async def get_conversation_history(
    session_id: str,
    limit: int = 10
) -> List[dict]:
    """
    获取对话历史
    
    - **session_id**: 会话ID
    - **limit**: 返回记录数量限制（默认10条）
    """
    try:
        history = await qa_service.get_conversation_history(session_id, limit)
        
        logger.info(
            "获取对话历史成功",
            session_id=session_id,
            count=len(history)
        )
        
        return history
        
    except Exception as e:
        logger.error(
            "获取对话历史失败",
            session_id=session_id,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "HISTORY_FETCH_ERROR",
                "message": "获取对话历史失败",
                "detail": str(e)
            }
        )


@router.post(
    "/search",
    response_model=VectorSearchResponse,
    summary="向量搜索接口",
    description="直接调用向量搜索功能，用于测试和调试"
)
async def vector_search(request: VectorSearchRequest) -> VectorSearchResponse:
    """
    向量搜索
    
    - **query**: 搜索查询
    - **collection**: 集合名称
    - **top_k**: 返回结果数量
    - **threshold**: 相似度阈值
    """
    try:
        logger.info(
            "收到向量搜索请求",
            query_length=len(request.query),
            collection=request.collection,
            top_k=request.top_k,
            threshold=request.threshold
        )
        
        # 调用向量搜索服务
        results = await vector_service.client.search(
            query=request.query,
            collection=request.collection,
            top_k=request.top_k,
            threshold=request.threshold
        )
        
        response = VectorSearchResponse(
            results=results,
            total=len(results),
            query=request.query,
            collection=request.collection
        )
        
        logger.info(
            "向量搜索完成",
            collection=request.collection,
            results_count=len(results)
        )
        
        return response
        
    except CustomException as e:
        logger.error(
            "向量搜索失败",
            error_code=e.error_code,
            message=e.message
        )
        raise HTTPException(
            status_code=e.status_code,
            detail={
                "error_code": e.error_code,
                "message": e.message,
                "detail": e.detail
            }
        )
    except Exception as e:
        logger.error("向量搜索异常", error=str(e), exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "SEARCH_ERROR",
                "message": "向量搜索失败",
                "detail": str(e)
            }
        )
