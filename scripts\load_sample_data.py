#!/usr/bin/env python3
"""
加载示例数据脚本
用于向向量数据库中加载测试数据
"""
import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.vector_service import vector_service
from app.core.logging import setup_logging
import structlog

# 设置日志
setup_logging()
logger = structlog.get_logger(__name__)


# 示例预设答案数据
PRESET_ANSWERS = [
    {
        "id": "preset_001",
        "content": "要重置密码，请点击登录页面的'忘记密码'链接，然后输入您的邮箱地址。系统会发送重置链接到您的邮箱。",
        "question": "如何重置密码？",
        "category": "account",
        "tags": ["密码", "重置", "登录"]
    },
    {
        "id": "preset_002",
        "content": "您可以在个人设置页面修改您的个人信息，包括姓名、邮箱、电话等。点击右上角头像，选择'个人设置'即可。",
        "question": "如何修改个人信息？",
        "category": "profile",
        "tags": ["个人信息", "设置", "修改"]
    },
    {
        "id": "preset_003",
        "content": "系统支持多种文件格式上传，包括PDF、Word、Excel、图片等。单个文件大小不能超过10MB。",
        "question": "支持哪些文件格式上传？",
        "category": "upload",
        "tags": ["文件", "上传", "格式"]
    },
    {
        "id": "preset_004",
        "content": "如果遇到系统错误，请先刷新页面重试。如果问题持续存在，请联系技术支持：<EMAIL>",
        "question": "遇到系统错误怎么办？",
        "category": "support",
        "tags": ["错误", "支持", "联系"]
    },
    {
        "id": "preset_005",
        "content": "您的数据会定期自动备份，同时我们采用多重安全措施保护您的隐私和数据安全。",
        "question": "数据安全如何保障？",
        "category": "security",
        "tags": ["数据", "安全", "备份"]
    }
]

# 示例知识库数据
KNOWLEDGE_BASE = [
    {
        "id": "kb_001",
        "content": "用户账户管理系统提供完整的用户生命周期管理功能，包括注册、登录、密码管理、个人信息维护等。系统采用多因素认证确保账户安全。",
        "title": "用户账户管理",
        "category": "user_management",
        "source": "user_manual.pdf",
        "tags": ["用户", "账户", "管理", "安全"]
    },
    {
        "id": "kb_002",
        "content": "文件管理系统支持多种文件操作，包括上传、下载、预览、分享等。支持的文件格式包括：文档类（PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX）、图片类（JPG、PNG、GIF、BMP）、压缩包（ZIP、RAR）等。",
        "title": "文件管理功能",
        "category": "file_management",
        "source": "feature_guide.pdf",
        "tags": ["文件", "上传", "下载", "格式"]
    },
    {
        "id": "kb_003",
        "content": "系统安全策略包括：数据加密传输、定期安全扫描、访问权限控制、操作日志记录、数据备份恢复等。所有敏感数据都经过加密存储。",
        "title": "系统安全策略",
        "category": "security",
        "source": "security_policy.pdf",
        "tags": ["安全", "加密", "权限", "备份"]
    },
    {
        "id": "kb_004",
        "content": "API接口文档详细说明了所有可用的API端点、请求参数、响应格式和错误代码。开发者可以通过API实现系统集成和自定义功能开发。",
        "title": "API接口文档",
        "category": "development",
        "source": "api_docs.pdf",
        "tags": ["API", "接口", "开发", "集成"]
    },
    {
        "id": "kb_005",
        "content": "故障排除指南提供了常见问题的解决方案，包括登录问题、文件上传失败、页面加载缓慢、功能异常等。大部分问题可以通过清除浏览器缓存或重启应用解决。",
        "title": "故障排除指南",
        "category": "troubleshooting",
        "source": "troubleshooting.pdf",
        "tags": ["故障", "排除", "问题", "解决"]
    },
    {
        "id": "kb_006",
        "content": "系统配置管理允许管理员自定义系统行为，包括用户权限设置、功能模块开关、界面主题配置、通知设置等。配置更改会实时生效。",
        "title": "系统配置管理",
        "category": "configuration",
        "source": "admin_guide.pdf",
        "tags": ["配置", "管理", "设置", "权限"]
    }
]


async def load_preset_answers():
    """加载预设答案到向量数据库"""
    try:
        logger.info("开始加载预设答案数据...")
        
        success = await vector_service.client.insert(
            collection="preset_answers",
            documents=PRESET_ANSWERS
        )
        
        if success:
            logger.info("预设答案数据加载成功", count=len(PRESET_ANSWERS))
        else:
            logger.error("预设答案数据加载失败")
            
    except Exception as e:
        logger.error("加载预设答案数据时发生错误", error=str(e))


async def load_knowledge_base():
    """加载知识库数据到向量数据库"""
    try:
        logger.info("开始加载知识库数据...")
        
        success = await vector_service.client.insert(
            collection="knowledge_base",
            documents=KNOWLEDGE_BASE
        )
        
        if success:
            logger.info("知识库数据加载成功", count=len(KNOWLEDGE_BASE))
        else:
            logger.error("知识库数据加载失败")
            
    except Exception as e:
        logger.error("加载知识库数据时发生错误", error=str(e))


async def test_search():
    """测试搜索功能"""
    try:
        logger.info("测试向量搜索功能...")
        
        # 测试预设答案搜索
        test_queries = [
            "怎么重置密码",
            "如何上传文件",
            "数据安全吗",
            "系统出错了"
        ]
        
        for query in test_queries:
            logger.info(f"搜索查询: {query}")
            
            # 搜索预设答案
            preset_result = await vector_service.search_preset_answers(query)
            if preset_result:
                logger.info(
                    "找到预设答案",
                    score=preset_result.score,
                    content=preset_result.content[:50] + "..."
                )
            else:
                logger.info("未找到预设答案")
            
            # 搜索知识库
            knowledge_results = await vector_service.search_knowledge_base(query)
            logger.info(
                "知识库搜索结果",
                count=len(knowledge_results),
                scores=[r.score for r in knowledge_results[:3]]
            )
            
            print("-" * 50)
            
    except Exception as e:
        logger.error("测试搜索功能时发生错误", error=str(e))


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="加载示例数据脚本")
    parser.add_argument(
        "--action",
        choices=["load", "test", "all"],
        default="all",
        help="要执行的操作"
    )
    parser.add_argument(
        "--collection",
        choices=["preset", "knowledge", "both"],
        default="both",
        help="要加载的数据集合"
    )
    
    args = parser.parse_args()
    
    try:
        if args.action in ["load", "all"]:
            if args.collection in ["preset", "both"]:
                await load_preset_answers()
            
            if args.collection in ["knowledge", "both"]:
                await load_knowledge_base()
        
        if args.action in ["test", "all"]:
            await test_search()
            
        logger.info("脚本执行完成")
        
    except Exception as e:
        logger.error("脚本执行失败", error=str(e))
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
