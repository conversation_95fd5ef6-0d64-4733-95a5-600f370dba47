"""
管理相关API端点
"""
from typing import List
from fastapi import APIRouter, HTTPException, Depends
import structlog

from app.models.schemas import <PERSON><PERSON><PERSON>ey<PERSON><PERSON>, ApiKeyResponse
from app.core.security import key_manager
from app.db.database import get_db_session
from app.db.models import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy import select

logger = structlog.get_logger(__name__)
router = APIRouter()


@router.post(
    "/api-keys",
    response_model=dict,
    summary="添加API密钥",
    description="添加新的API密钥到系统中"
)
async def create_api_key(request: ApiKeyCreate) -> dict:
    """
    创建API密钥
    
    - **provider**: 提供商名称（如：openai, anthropic）
    - **key_type**: 密钥类型（默认：default）
    - **key_value**: 密钥值
    - **description**: 描述信息
    """
    try:
        logger.info(
            "创建API密钥请求",
            provider=request.provider,
            key_type=request.key_type
        )
        
        # 存储密钥
        success = await key_manager.store_api_key(
            provider=request.provider,
            key_value=request.key_value,
            key_type=request.key_type
        )
        
        if success:
            logger.info(
                "API密钥创建成功",
                provider=request.provider,
                key_type=request.key_type
            )
            return {
                "message": "API密钥创建成功",
                "provider": request.provider,
                "key_type": request.key_type
            }
        else:
            raise HTTPException(
                status_code=500,
                detail={
                    "error_code": "KEY_CREATION_FAILED",
                    "message": "API密钥创建失败"
                }
            )
            
    except Exception as e:
        logger.error(
            "创建API密钥失败",
            provider=request.provider,
            error=str(e)
        )
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "KEY_CREATION_ERROR",
                "message": "创建API密钥时发生错误",
                "detail": str(e)
            }
        )


@router.get(
    "/api-keys",
    response_model=List[ApiKeyResponse],
    summary="获取API密钥列表",
    description="获取系统中配置的API密钥列表（不包含密钥值）"
)
async def list_api_keys() -> List[ApiKeyResponse]:
    """
    获取API密钥列表
    
    返回所有配置的API密钥信息，但不包含实际的密钥值
    """
    try:
        async with get_db_session() as session:
            stmt = select(ApiKey).where(ApiKey.is_active == True)
            result = await session.execute(stmt)
            api_keys = result.scalars().all()
            
            response = []
            for key in api_keys:
                response.append(ApiKeyResponse(
                    id=key.id,
                    provider=key.provider,
                    key_type=key.key_type,
                    description=key.description,
                    is_active=key.is_active,
                    created_at=key.created_at,
                    updated_at=key.updated_at
                ))
            
            logger.info("获取API密钥列表成功", count=len(response))
            return response
            
    except Exception as e:
        logger.error("获取API密钥列表失败", error=str(e))
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "KEY_LIST_ERROR",
                "message": "获取API密钥列表失败",
                "detail": str(e)
            }
        )


@router.delete(
    "/api-keys/{key_id}",
    summary="删除API密钥",
    description="删除指定的API密钥"
)
async def delete_api_key(key_id: int) -> dict:
    """
    删除API密钥
    
    - **key_id**: 密钥ID
    """
    try:
        async with get_db_session() as session:
            stmt = select(ApiKey).where(ApiKey.id == key_id)
            result = await session.execute(stmt)
            api_key = result.scalar_one_or_none()
            
            if not api_key:
                raise HTTPException(
                    status_code=404,
                    detail={
                        "error_code": "KEY_NOT_FOUND",
                        "message": "API密钥不存在"
                    }
                )
            
            # 软删除：设置为非活跃状态
            api_key.is_active = False
            await session.commit()
            
            # 清空缓存
            key_manager.clear_cache()
            
            logger.info(
                "API密钥删除成功",
                key_id=key_id,
                provider=api_key.provider,
                key_type=api_key.key_type
            )
            
            return {
                "message": "API密钥删除成功",
                "key_id": key_id
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error("删除API密钥失败", key_id=key_id, error=str(e))
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "KEY_DELETE_ERROR",
                "message": "删除API密钥失败",
                "detail": str(e)
            }
        )


@router.post(
    "/cache/clear",
    summary="清空缓存",
    description="清空系统缓存"
)
async def clear_cache() -> dict:
    """清空系统缓存"""
    try:
        # 清空密钥缓存
        key_manager.clear_cache()
        
        # 这里可以添加其他缓存清理逻辑
        # 如：Redis缓存、模型缓存等
        
        logger.info("系统缓存清空成功")
        
        return {
            "message": "系统缓存清空成功",
            "timestamp": "now"
        }
        
    except Exception as e:
        logger.error("清空缓存失败", error=str(e))
        raise HTTPException(
            status_code=500,
            detail={
                "error_code": "CACHE_CLEAR_ERROR",
                "message": "清空缓存失败",
                "detail": str(e)
            }
        )
